import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExternalLinkAlt } from 'react-icons/fa';
import { ActivityData } from '../SmartWalletDetail';

interface ActivityTableProps {
  walletAddress: string;
}

const ActivityTable: React.FC<ActivityTableProps> = ({ walletAddress }) => {
  const [data, setData] = useState<ActivityData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(decimals);
  };

  const formatPnL = (value: number): { text: string; color: string } => {
    const isPositive = value >= 0;
    const formattedValue = `${isPositive ? '+' : ''}$${formatNumber(Math.abs(value))}`;
    return {
      text: formattedValue,
      color: isPositive ? 'text-green-400' : 'text-red-400'
    };
  };

  // Mock data for demonstration
  const mockData: ActivityData[] = [
    {
      id: '1',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'WIF',
      tokenName: 'dogwifhat',
      tokenImage: '/api/placeholder/24/24',
      total: 979.95,
      amount: 109.1,
      price: 0.067123,
      profit: 186.09,
      duration: '3m ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T10:30:00Z'
    },
    {
      id: '2',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'WIF',
      tokenName: 'dogwifhat',
      tokenImage: '/api/placeholder/24/24',
      total: 793.86,
      amount: 109.1,
      price: 0.06804,
      profit: 0,
      duration: '1h ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T10:27:00Z'
    },
    {
      id: '3',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      tokenImage: '/api/placeholder/24/24',
      total: 582.40,
      amount: 10760.0,
      price: 0.000054,
      profit: 234.56,
      duration: '2h ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T10:24:00Z'
    },
    {
      id: '4',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'BONK',
      tokenName: 'Bonk',
      tokenImage: '/api/placeholder/24/24',
      total: 347.84,
      amount: 10760.0,
      price: 0.000032,
      profit: 0,
      duration: '4h ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T10:22:00Z'
    },
    {
      id: '5',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'GOAT',
      tokenName: 'Goatseus Maximus',
      tokenImage: '/api/placeholder/24/24',
      total: 1585.37,
      amount: 98.3,
      price: 0.16123,
      profit: 567.89,
      duration: '6h ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T10:17:00Z'
    },
    {
      id: '6',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'GOAT',
      tokenName: 'Goatseus Maximus',
      tokenImage: '/api/placeholder/24/24',
      total: 1017.48,
      amount: 98.3,
      price: 0.10352,
      profit: 0,
      duration: '8h ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T10:14:00Z'
    },
    {
      id: '7',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'POPCAT',
      tokenName: 'Popcat',
      tokenImage: '/api/placeholder/24/24',
      total: 456.78,
      amount: 45.6,
      price: 0.10017,
      profit: 123.45,
      duration: '12h ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-15T08:30:00Z'
    },
    {
      id: '8',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'POPCAT',
      tokenName: 'Popcat',
      tokenImage: '/api/placeholder/24/24',
      total: 333.33,
      amount: 45.6,
      price: 0.07310,
      profit: 0,
      duration: '1d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-14T20:15:00Z'
    },
    {
      id: '9',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'MOODENG',
      tokenName: 'Moo Deng',
      tokenImage: '/api/placeholder/24/24',
      total: 789.12,
      amount: 234.5,
      price: 0.03366,
      profit: 345.67,
      duration: '1d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-14T18:45:00Z'
    },
    {
      id: '10',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'MOODENG',
      tokenName: 'Moo Deng',
      tokenImage: '/api/placeholder/24/24',
      total: 443.45,
      amount: 234.5,
      price: 0.01891,
      profit: 0,
      duration: '2d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-13T14:20:00Z'
    },
    {
      id: '11',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'PNUT',
      tokenName: 'Peanut the Squirrel',
      tokenImage: '/api/placeholder/24/24',
      total: 1234.56,
      amount: 567.8,
      price: 0.02175,
      profit: -89.12,
      duration: '2d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-13T11:30:00Z'
    },
    {
      id: '12',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'PNUT',
      tokenName: 'Peanut the Squirrel',
      tokenImage: '/api/placeholder/24/24',
      total: 1323.68,
      amount: 567.8,
      price: 0.02331,
      profit: 0,
      duration: '3d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-12T16:45:00Z'
    },
    {
      id: '13',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'FWOG',
      tokenName: 'FWOG',
      tokenImage: '/api/placeholder/24/24',
      total: 678.90,
      amount: 123.4,
      price: 0.05502,
      profit: 234.56,
      duration: '3d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-12T09:15:00Z'
    },
    {
      id: '14',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'FWOG',
      tokenName: 'FWOG',
      tokenImage: '/api/placeholder/24/24',
      total: 444.34,
      amount: 123.4,
      price: 0.03601,
      profit: 0,
      duration: '4d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-11T13:30:00Z'
    },
    {
      id: '15',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'CHILLGUY',
      tokenName: 'Just a chill guy',
      tokenImage: '/api/placeholder/24/24',
      total: 567.89,
      amount: 78.9,
      price: 0.07198,
      profit: 123.45,
      duration: '5d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-10T17:20:00Z'
    },
    {
      id: '16',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'CHILLGUY',
      tokenName: 'Just a chill guy',
      tokenImage: '/api/placeholder/24/24',
      total: 444.44,
      amount: 78.9,
      price: 0.05633,
      profit: 0,
      duration: '6d ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-09T12:45:00Z'
    },
    {
      id: '17',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'PONKE',
      tokenName: 'Ponke',
      tokenImage: '/api/placeholder/24/24',
      total: 1234.56,
      amount: 345.6,
      price: 0.03572,
      profit: 456.78,
      duration: '1w ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-08T15:30:00Z'
    },
    {
      id: '18',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'PONKE',
      tokenName: 'Ponke',
      tokenImage: '/api/placeholder/24/24',
      total: 777.78,
      amount: 345.6,
      price: 0.02251,
      profit: 0,
      duration: '1w ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-07T10:15:00Z'
    },
    {
      id: '19',
      activity: 'Sell',
      type: 'Sell',
      tokenSymbol: 'GIGA',
      tokenName: 'GIGA',
      tokenImage: '/api/placeholder/24/24',
      total: 890.12,
      amount: 156.7,
      price: 0.05680,
      profit: 345.67,
      duration: '1w ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-06T14:45:00Z'
    },
    {
      id: '20',
      activity: 'Buy',
      type: 'Buy',
      tokenSymbol: 'GIGA',
      tokenName: 'GIGA',
      tokenImage: '/api/placeholder/24/24',
      total: 544.45,
      amount: 156.7,
      price: 0.03474,
      profit: 0,
      duration: '2w ago',
      copyTradeHash: undefined,
      timestamp: '2024-01-05T11:20:00Z'
    }
  ];

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setData(mockData);
      } catch (error) {
        console.error('Error fetching activity data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [walletAddress]);

  const openTransaction = (hash: string) => {
    window.open(`https://solscan.io/tx/${hash}`, '_blank');
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex items-center">
          <FaSpinner className="animate-spin text-[#7FFFD4] text-2xl mr-3" />
          <span className="text-white text-lg">Loading activity data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Fixed Header */}
      <div className="bg-[#1A1E24] border-b border-gray-700/50 px-4 sm:px-6 py-4">
        <div className="grid grid-cols-9 gap-2 sm:gap-4 text-xs font-medium text-gray-400 uppercase tracking-wider">
          <div>Type</div>
          <div>Token</div>
          <div className="text-right">Total</div>
          <div className="text-right hidden sm:block">Amount</div>
          <div className="text-right hidden md:block">Price</div>
          <div className="text-right">Profit</div>
          <div className="text-right hidden lg:block">Duration</div>
          <div className="text-center hidden xl:block">Copy Trade Hash</div>
          <div className="text-center">Action</div>
        </div>
      </div>

      {/* Scrollable Body */}
      <div className="flex-1 overflow-y-auto custom-scrollbar">
        {data.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <div className="text-gray-400 text-lg mb-2">No activity data</div>
              <div className="text-gray-500 text-sm">No trading activity found for this wallet</div>
            </div>
          </div>
        ) : (
          <div className="divide-y divide-gray-800/50">
            {data.map((item) => {
              const profit = formatPnL(item.profit);

              return (
                <div key={item.id} className="px-4 sm:px-6 py-4 hover:bg-[#1F2329] transition-colors">
                  <div className="grid grid-cols-9 gap-2 sm:gap-4 items-center">
                    {/* Type */}
                    <div className={`font-medium text-sm ${
                      item.type === 'Buy' ? 'text-green-400' : 'text-red-400'
                    }`}>
                      {item.type}
                    </div>

                    {/* Token */}
                    <div className="flex items-center space-x-2">
                      <div className="w-6 h-6 bg-gradient-to-br from-[#7FFFD4] to-[#025FDA] rounded-full flex items-center justify-center">
                        <span className="text-black text-xs font-bold">
                          {item.tokenSymbol.slice(0, 1)}
                        </span>
                      </div>
                      <span className="text-white text-sm font-medium">{item.tokenSymbol}</span>
                    </div>

                    {/* Total */}
                    <div className="text-right text-white text-sm">
                      ${formatNumber(item.total)}
                    </div>

                    {/* Amount */}
                    <div className="text-right text-white text-sm hidden sm:block">
                      {formatNumber(item.amount)}
                    </div>

                    {/* Price */}
                    <div className="text-right text-white text-sm hidden md:block">
                      ${item.price.toFixed(6)}
                    </div>

                    {/* Profit */}
                    <div className={`text-right font-medium text-sm ${profit.color}`}>
                      {item.profit === 0 ? '--' : profit.text}
                    </div>

                    {/* Duration */}
                    <div className="text-right text-gray-400 text-sm hidden lg:block">
                      {item.duration}
                    </div>

                    {/* Copy Trade Hash */}
                    <div className="text-center hidden xl:block">
                      {item.copyTradeHash ? (
                        <button
                          onClick={() => openTransaction(item.copyTradeHash!)}
                          className="text-[#7FFFD4] hover:text-[#6EEEC4] transition-colors"
                          title="View transaction"
                        >
                          <FaExternalLinkAlt size={12} />
                        </button>
                      ) : (
                        <span className="text-gray-500 text-xs">--</span>
                      )}
                    </div>

                    {/* Action */}
                    <div className="text-center">
                      <button
                        onClick={() => openTransaction(`mock-tx-${item.id}`)}
                        className="text-gray-400 hover:text-[#7FFFD4] transition-colors"
                        title="View on Solscan"
                      >
                        <FaExternalLinkAlt size={12} />
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivityTable;
