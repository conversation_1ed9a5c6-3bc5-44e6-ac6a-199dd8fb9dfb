import React from 'react';

interface FilterTabsProps {
  activeFilter: string;
  onFilterChange: (filter: string) => void;
}

const FilterTabs: React.FC<FilterTabsProps> = ({ activeFilter, onFilterChange }) => {
  const filters = [
    'All',
    'Pump Smart Money',
    'KOL',
    'Early Sniper',
    'Long-Term Investor',
    'Meme Expert',
    'Degen',
    'AI Agent Expert',
    'Conservative Investor'
  ];

  return (
    <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-4 border border-gray-800/50 w-full">
      <div className="flex flex-wrap gap-2 w-full">
        {filters.map((filter) => (
          <button
            key={filter}
            onClick={() => onFilterChange(filter)}
            className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 ${
              activeFilter === filter
                ? 'bg-[#7FFFD4] text-black shadow-lg'
                : 'bg-[#2A2F36] text-gray-300 hover:bg-[#3A3F46] hover:text-white'
            }`}
          >
            {filter}
          </button>
        ))}
      </div>
    </div>
  );
};

export default FilterTabs;
