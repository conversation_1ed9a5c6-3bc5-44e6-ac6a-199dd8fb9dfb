import React from 'react';
import { TrendingUp, TrendingDown, Target, Clock, DollarSign, BarChart3 } from 'lucide-react';
import { WalletDetailData } from '../SmartWalletDetail';

interface WalletStatisticsProps {
  walletData: WalletDetailData;
}

const WalletStatistics: React.FC<WalletStatisticsProps> = ({ walletData }) => {
  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(decimals);
  };

  const formatPnL = (value: number): { text: string; color: string } => {
    const isPositive = value >= 0;
    const formattedValue = `${isPositive ? '+' : ''}$${formatNumber(Math.abs(value))}`;
    return {
      text: formattedValue,
      color: isPositive ? 'text-green-400' : 'text-red-400'
    };
  };

  const largestWin = formatPnL(walletData.largestWin);
  const largestLoss = formatPnL(walletData.largestLoss);

  const statisticsData = [
    {
      icon: <BarChart3 className="w-5 h-5" />,
      label: 'Total Trades',
      value: walletData.totalTrades.toString(),
      subValue: `${walletData.successfulTrades} successful`,
      color: 'text-blue-400',
      bgColor: 'bg-blue-400/10'
    },
    {
      icon: <Target className="w-5 h-5" />,
      label: 'Success Rate',
      value: `${((walletData.successfulTrades / walletData.totalTrades) * 100).toFixed(1)}%`,
      subValue: `${walletData.successfulTrades}/${walletData.totalTrades} trades`,
      color: 'text-green-400',
      bgColor: 'bg-green-400/10'
    },
    {
      icon: <Clock className="w-5 h-5" />,
      label: 'Avg Hold Time',
      value: walletData.avgHoldTime,
      subValue: 'per position',
      color: 'text-purple-400',
      bgColor: 'bg-purple-400/10'
    },
    {
      icon: <TrendingUp className="w-5 h-5" />,
      label: 'Largest Win',
      value: largestWin.text,
      subValue: 'single trade',
      color: largestWin.color,
      bgColor: 'bg-green-400/10'
    },
    {
      icon: <TrendingDown className="w-5 h-5" />,
      label: 'Largest Loss',
      value: largestLoss.text,
      subValue: 'single trade',
      color: largestLoss.color,
      bgColor: 'bg-red-400/10'
    },
    {
      icon: <DollarSign className="w-5 h-5" />,
      label: 'Profit Factor',
      value: walletData.profitFactor.toFixed(1),
      subValue: 'gross profit/loss ratio',
      color: 'text-[#7FFFD4]',
      bgColor: 'bg-[#7FFFD4]/10'
    }
  ];

  return (
    <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl border border-gray-800/50 p-4 sm:p-6">
      <div className="mb-4 sm:mb-6">
        <h3 className="text-lg sm:text-xl font-bold text-white mb-2">Performance Statistics</h3>
        <p className="text-gray-400 text-sm">Detailed trading performance metrics and analysis</p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {statisticsData.map((stat, index) => (
          <div
            key={index}
            className="bg-[#1A1E24]/60 rounded-lg p-4 border border-gray-800/30 hover:border-gray-700/50 transition-colors"
          >
            <div className="flex items-start justify-between mb-3">
              <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                <div className={stat.color}>
                  {stat.icon}
                </div>
              </div>
            </div>
            
            <div className="space-y-1">
              <div className="text-gray-400 text-sm font-medium">
                {stat.label}
              </div>
              <div className={`text-2xl font-bold ${stat.color}`}>
                {stat.value}
              </div>
              <div className="text-gray-500 text-xs">
                {stat.subValue}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Additional Summary Row */}
      <div className="mt-4 sm:mt-6 pt-4 sm:pt-6 border-t border-gray-800/50">
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-6">
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Win/Loss Ratio</div>
            <div className="text-white text-lg font-semibold">
              {(walletData.successfulTrades / (walletData.totalTrades - walletData.successfulTrades)).toFixed(2)}:1
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Avg Profit per Trade</div>
            <div className="text-green-400 text-lg font-semibold">
              +${(walletData.totalPnL / walletData.totalTrades).toFixed(2)}
            </div>
          </div>
          
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Risk Score</div>
            <div className="text-yellow-400 text-lg font-semibold">
              {walletData.profitFactor > 3 ? 'Low' : walletData.profitFactor > 2 ? 'Medium' : 'High'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WalletStatistics;
