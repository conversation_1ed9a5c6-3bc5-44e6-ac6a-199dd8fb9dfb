import React, { useState } from 'react';
import { <PERSON>a<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, FaExternalLinkAlt } from 'react-icons/fa';
import { WalletDetailData } from '../SmartWalletDetail';

interface WalletOverviewHeaderProps {
  walletData: WalletDetailData;
}

const WalletOverviewHeader: React.FC<WalletOverviewHeaderProps> = ({ walletData }) => {
  const [copiedAddress, setCopiedAddress] = useState<boolean>(false);

  const formatNumber = (num: number, decimals: number = 2): string => {
    if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toFixed(decimals);
  };

  const formatPnL = (value: number): { text: string; color: string } => {
    const isPositive = value >= 0;
    const formattedValue = `${isPositive ? '+' : ''}$${formatNumber(Math.abs(value))}`;
    return {
      text: formattedValue,
      color: isPositive ? 'text-green-400' : 'text-red-400'
    };
  };

  const copyToClipboard = (address: string) => {
    navigator.clipboard.writeText(address);
    setCopiedAddress(true);
    setTimeout(() => setCopiedAddress(false), 2000);
  };

  const openInSolscan = (address: string) => {
    window.open(`https://solscan.io/account/${address}`, '_blank');
  };

  const totalPnL = formatPnL(walletData.totalPnL);
  const last7DPnL = formatPnL(walletData.last7DPnL);

  return (
    <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl border border-gray-800/50 overflow-hidden">
      {/* Header Section */}
      <div className="bg-gradient-to-r from-[#7FFFD4]/10 to-[#025FDA]/10 p-4 sm:p-6 border-b border-gray-800/50">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Wallet Avatar */}
            <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gradient-to-br from-[#7FFFD4] to-[#025FDA] rounded-full flex items-center justify-center flex-shrink-0">
              <span className="text-black text-lg sm:text-xl font-bold">
                {walletData.walletAddress.slice(0, 2)}
              </span>
            </div>

            {/* Wallet Info */}
            <div className="min-w-0 flex-1">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-3 mb-2">
                <h2 className="text-lg sm:text-2xl font-bold text-white truncate">{walletData.walletAddress}</h2>
                <button
                  onClick={() => copyToClipboard(walletData.walletAddress)}
                  className="text-gray-400 hover:text-[#7FFFD4] transition-colors"
                  title="Copy address"
                >
                  {copiedAddress ? (
                    <FaCheck size={16} className="text-green-400" />
                  ) : (
                    <FaCopy size={16} />
                  )}
                </button>
                <button
                  onClick={() => openInSolscan(walletData.walletAddress)}
                  className="text-gray-400 hover:text-[#7FFFD4] transition-colors"
                  title="View on Solscan"
                >
                  <FaExternalLinkAlt size={14} />
                </button>
              </div>
              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                <span className="px-3 py-1 bg-[#7FFFD4]/20 text-[#7FFFD4] rounded-full text-sm font-medium w-fit">
                  {walletData.category}
                </span>
                <span className="text-gray-400 text-sm">
                  Last active: {walletData.lastActivity}
                </span>
              </div>
            </div>
          </div>

          {/* Copy Trade Button */}
          <div className="flex items-center justify-center lg:justify-end space-x-3">
            <button className="bg-[#7FFFD4] text-black px-4 sm:px-6 py-2 rounded-lg font-medium hover:bg-[#6EEEC4] transition-colors text-sm sm:text-base">
              Copy Trade
            </button>
          </div>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="p-4 sm:p-6">
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 sm:gap-6">
          {/* SOL Balance */}
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">SOL Balance</div>
            <div className="text-white text-lg font-semibold">
              {formatNumber(walletData.solBalance)} SOL
            </div>
          </div>

          {/* Total PnL */}
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Total PnL</div>
            <div className={`text-lg font-semibold ${totalPnL.color}`}>
              {totalPnL.text}
            </div>
          </div>

          {/* Last 7D PnL */}
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Last 7D PnL</div>
            <div className={`text-lg font-semibold ${last7DPnL.color}`}>
              {last7DPnL.text}
            </div>
            <div className="text-xs text-gray-500">+5.3%</div>
          </div>

          {/* Win Rate */}
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Win Rate</div>
            <div className="text-white text-lg font-semibold">
              {walletData.winRate.toFixed(1)}%
            </div>
          </div>

          {/* 7D Volume */}
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">7D Volume</div>
            <div className="text-white text-lg font-semibold">
              {formatNumber(walletData.volume7D)}K
            </div>
            <div className="text-xs text-gray-500">{formatNumber(walletData.volume7D * 2.4)}K</div>
          </div>

          {/* Unrealized Profits */}
          <div className="text-center">
            <div className="text-gray-400 text-sm mb-1">Unrealized Profits</div>
            <div className="text-white text-lg font-semibold">
              ${formatNumber(walletData.unrealizedProfits)}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default WalletOverviewHeader;
