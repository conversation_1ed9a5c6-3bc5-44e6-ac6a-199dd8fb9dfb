import React, { useState, useEffect } from 'react';
import Navbar from '@/Home/Navbar/Navbar';
import SmartWalletTable from './components/SmartWalletTable';
import FilterTabs from './components/FilterTabs';

export interface SmartWalletData {
  id: string;
  walletAddress: string;
  solBalance: number;
  pnl1d: number;
  pnl7d: number;
  pnl30d: number;
  winRate7d: number;
  transactions7d: number;
  volume7d: number;
  profit7d: number;
  profit7dChart: number[];
  lastActivity: string;
  category: string;
}

const SmartWallet = () => {
  const [activeFilter, setActiveFilter] = useState<string>('All');
  const [smartWalletData, setSmartWalletData] = useState<SmartWalletData[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  // Mock data for demonstration - replace with actual API call
  const mockData: SmartWalletData[] = [
    // Pump Smart Money Wallets
    {
      id: '1',
      walletAddress: '8Cag...1JPd',
      solBalance: 2500.7,
      pnl1d: 388.6,
      pnl7d: 846.7,
      pnl30d: 1904,
      winRate7d: 62.9,
      transactions7d: 57,
      volume7d: 16.5,
      profit7d: 16.5,
      profit7dChart: [12, 14, 13, 15, 16, 18, 16.5],
      lastActivity: '7h ago',
      category: 'Pump Smart Money'
    },
    {
      id: '9',
      walletAddress: '9XmK...4RtP',
      solBalance: 3200.4,
      pnl1d: 512.8,
      pnl7d: 1205.3,
      pnl30d: 2847.6,
      winRate7d: 68.2,
      transactions7d: 73,
      volume7d: 24.8,
      profit7d: 24.8,
      profit7dChart: [18, 20, 22, 24, 23, 25, 24.8],
      lastActivity: '3h ago',
      category: 'Pump Smart Money'
    },
    {
      id: '10',
      walletAddress: 'FmNx...8QwE',
      solBalance: 1890.2,
      pnl1d: 298.7,
      pnl7d: 672.1,
      pnl30d: 1456.8,
      winRate7d: 59.4,
      transactions7d: 42,
      volume7d: 12.3,
      profit7d: 12.3,
      profit7dChart: [9, 10, 11, 12, 11.5, 12.8, 12.3],
      lastActivity: '5h ago',
      category: 'Pump Smart Money'
    },
    {
      id: '11',
      walletAddress: 'KpLm...7VcD',
      solBalance: 4150.6,
      pnl1d: 623.4,
      pnl7d: 1456.2,
      pnl30d: 3892.1,
      winRate7d: 71.8,
      transactions7d: 89,
      volume7d: 32.7,
      profit7d: 32.7,
      profit7dChart: [25, 28, 30, 32, 31, 33, 32.7],
      lastActivity: '2h ago',
      category: 'Pump Smart Money'
    },

    // KOL Wallets
    {
      id: '2',
      walletAddress: 'HYWo...1ENp',
      solBalance: 1840,
      pnl1d: 271.2,
      pnl7d: 846.7,
      pnl30d: 3304,
      winRate7d: 41.7,
      transactions7d: 288,
      volume7d: 7.6,
      profit7d: 7.6,
      profit7dChart: [5, 6, 7, 8, 7.5, 7.8, 7.6],
      lastActivity: '4h ago',
      category: 'KOL'
    },
    {
      id: '12',
      walletAddress: 'TxRs...9MnB',
      solBalance: 2340.8,
      pnl1d: 387.2,
      pnl7d: 1124.6,
      pnl30d: 4567.3,
      winRate7d: 45.3,
      transactions7d: 356,
      volume7d: 15.2,
      profit7d: 15.2,
      profit7dChart: [12, 13, 14, 15, 14.5, 15.8, 15.2],
      lastActivity: '1h ago',
      category: 'KOL'
    },
    {
      id: '13',
      walletAddress: 'WqEr...3ZxC',
      solBalance: 1567.3,
      pnl1d: 234.1,
      pnl7d: 678.9,
      pnl30d: 2890.4,
      winRate7d: 38.9,
      transactions7d: 198,
      volume7d: 9.8,
      profit7d: 9.8,
      profit7dChart: [7, 8, 9, 10, 9.5, 10.2, 9.8],
      lastActivity: '6h ago',
      category: 'KOL'
    },

    // Early Sniper Wallets
    {
      id: '3',
      walletAddress: '3pZS...mSdj',
      solBalance: 1560,
      pnl1d: 437.1,
      pnl7d: 244.1,
      pnl30d: 537.8,
      winRate7d: 63.6,
      transactions7d: 13,
      volume7d: 4.5,
      profit7d: 4.5,
      profit7dChart: [3, 4, 4.2, 4.8, 4.6, 4.7, 4.5],
      lastActivity: '5h ago',
      category: 'Early Sniper'
    },
    {
      id: '14',
      walletAddress: 'YuIo...6HgF',
      solBalance: 2890.7,
      pnl1d: 578.3,
      pnl7d: 456.8,
      pnl30d: 1234.5,
      winRate7d: 78.9,
      transactions7d: 19,
      volume7d: 8.9,
      profit7d: 8.9,
      profit7dChart: [6, 7, 8, 9, 8.5, 9.2, 8.9],
      lastActivity: '8h ago',
      category: 'Early Sniper'
    },
    {
      id: '15',
      walletAddress: 'AsDF...2JkL',
      solBalance: 3456.2,
      pnl1d: 692.4,
      pnl7d: 567.3,
      pnl30d: 1567.8,
      winRate7d: 82.4,
      transactions7d: 17,
      volume7d: 11.2,
      profit7d: 11.2,
      profit7dChart: [8, 9, 10, 11, 10.5, 11.5, 11.2],
      lastActivity: '4h ago',
      category: 'Early Sniper'
    },
    {
      id: '16',
      walletAddress: 'QwEr...5TyU',
      solBalance: 1234.9,
      pnl1d: 345.6,
      pnl7d: 234.7,
      pnl30d: 678.9,
      winRate7d: 75.0,
      transactions7d: 12,
      volume7d: 5.6,
      profit7d: 5.6,
      profit7dChart: [4, 5, 5.2, 5.8, 5.4, 5.9, 5.6],
      lastActivity: '12h ago',
      category: 'Early Sniper'
    },

    // Long-Term Investor Wallets
    {
      id: '4',
      walletAddress: 'DMYx...Xhzj',
      solBalance: 890.3,
      pnl1d: 59.3,
      pnl7d: 222.5,
      pnl30d: 959.5,
      winRate7d: 81.5,
      transactions7d: 24,
      volume7d: 5.5,
      profit7d: 5.5,
      profit7dChart: [4.5, 5, 5.2, 5.8, 5.6, 5.7, 5.5],
      lastActivity: '5h ago',
      category: 'Long-Term Investor'
    },
    {
      id: '17',
      walletAddress: 'ZxCv...8BnM',
      solBalance: 5678.4,
      pnl1d: 123.4,
      pnl7d: 567.8,
      pnl30d: 2345.6,
      winRate7d: 85.7,
      transactions7d: 35,
      volume7d: 18.9,
      profit7d: 18.9,
      profit7dChart: [15, 16, 17, 18, 17.5, 19, 18.9],
      lastActivity: '10h ago',
      category: 'Long-Term Investor'
    },
    {
      id: '18',
      walletAddress: 'PoIu...4YtR',
      solBalance: 3456.7,
      pnl1d: 89.2,
      pnl7d: 345.6,
      pnl30d: 1456.7,
      winRate7d: 79.3,
      transactions7d: 29,
      volume7d: 12.4,
      profit7d: 12.4,
      profit7dChart: [10, 11, 12, 13, 12.2, 12.8, 12.4],
      lastActivity: '14h ago',
      category: 'Long-Term Investor'
    },

    // Meme Expert Wallets
    {
      id: '5',
      walletAddress: '3WG...P2xp',
      solBalance: 750,
      pnl1d: 61.6,
      pnl7d: 221.6,
      pnl30d: 456.3,
      winRate7d: 6.5,
      transactions7d: 51,
      volume7d: 2.5,
      profit7d: 2.5,
      profit7dChart: [2, 2.2, 2.4, 2.6, 2.5, 2.7, 2.5],
      lastActivity: '7h ago',
      category: 'Meme Expert'
    },
    {
      id: '19',
      walletAddress: 'MnBv...7CxZ',
      solBalance: 1234.5,
      pnl1d: 156.7,
      pnl7d: 456.8,
      pnl30d: 987.6,
      winRate7d: 42.3,
      transactions7d: 89,
      volume7d: 6.7,
      profit7d: 6.7,
      profit7dChart: [5, 5.5, 6, 6.5, 6.2, 6.9, 6.7],
      lastActivity: '3h ago',
      category: 'Meme Expert'
    },
    {
      id: '20',
      walletAddress: 'LkJh...9GfD',
      solBalance: 987.3,
      pnl1d: 98.7,
      pnl7d: 234.5,
      pnl30d: 567.8,
      winRate7d: 38.9,
      transactions7d: 67,
      volume7d: 4.2,
      profit7d: 4.2,
      profit7dChart: [3.5, 3.8, 4, 4.2, 4.1, 4.3, 4.2],
      lastActivity: '9h ago',
      category: 'Meme Expert'
    },
    {
      id: '21',
      walletAddress: 'HgFd...3SaQ',
      solBalance: 1567.8,
      pnl1d: 234.5,
      pnl7d: 567.8,
      pnl30d: 1234.5,
      winRate7d: 45.6,
      transactions7d: 123,
      volume7d: 8.9,
      profit7d: 8.9,
      profit7dChart: [7, 7.5, 8, 8.5, 8.2, 9.1, 8.9],
      lastActivity: '6h ago',
      category: 'Meme Expert'
    },

    // Degen Wallets
    {
      id: '6',
      walletAddress: 'B5Ru...FyLJ',
      solBalance: 1456,
      pnl1d: 164.5,
      pnl7d: 166.5,
      pnl30d: 391.8,
      winRate7d: 44.4,
      transactions7d: 445,
      volume7d: 1.5,
      profit7d: 1.5,
      profit7dChart: [1.2, 1.3, 1.4, 1.6, 1.5, 1.6, 1.5],
      lastActivity: '2h ago',
      category: 'Degen'
    },
    {
      id: '22',
      walletAddress: 'QaZw...8SxE',
      solBalance: 678.9,
      pnl1d: -45.6,
      pnl7d: 123.4,
      pnl30d: 234.5,
      winRate7d: 32.1,
      transactions7d: 567,
      volume7d: 3.4,
      profit7d: 3.4,
      profit7dChart: [2.8, 3, 3.2, 3.4, 3.1, 3.5, 3.4],
      lastActivity: '1h ago',
      category: 'Degen'
    },
    {
      id: '23',
      walletAddress: 'EdCr...5VfB',
      solBalance: 890.1,
      pnl1d: 67.8,
      pnl7d: -89.2,
      pnl30d: 345.6,
      winRate7d: 28.7,
      transactions7d: 789,
      volume7d: 2.1,
      profit7d: 2.1,
      profit7dChart: [1.8, 2, 2.1, 2.3, 2.0, 2.2, 2.1],
      lastActivity: '4h ago',
      category: 'Degen'
    },
    {
      id: '24',
      walletAddress: 'RfVt...2GbY',
      solBalance: 1123.4,
      pnl1d: 112.3,
      pnl7d: 234.5,
      pnl30d: -123.4,
      winRate7d: 35.8,
      transactions7d: 456,
      volume7d: 4.5,
      profit7d: 4.5,
      profit7dChart: [3.8, 4, 4.2, 4.5, 4.1, 4.6, 4.5],
      lastActivity: '7h ago',
      category: 'Degen'
    },

    // AI Agent Expert Wallets
    {
      id: '7',
      walletAddress: 'Anza...LxXH',
      solBalance: 1890,
      pnl1d: 61.8,
      pnl7d: 111.4,
      pnl30d: 170.6,
      winRate7d: 70.3,
      transactions7d: 897,
      volume7d: 2.4,
      profit7d: 2.4,
      profit7dChart: [2.1, 2.2, 2.3, 2.5, 2.4, 2.5, 2.4],
      lastActivity: '1h ago',
      category: 'AI Agent Expert'
    },
    {
      id: '25',
      walletAddress: 'TgBh...4NjM',
      solBalance: 2345.6,
      pnl1d: 234.5,
      pnl7d: 456.7,
      pnl30d: 789.1,
      winRate7d: 76.4,
      transactions7d: 234,
      volume7d: 12.3,
      profit7d: 12.3,
      profit7dChart: [10, 10.5, 11, 12, 11.5, 12.5, 12.3],
      lastActivity: '5h ago',
      category: 'AI Agent Expert'
    },
    {
      id: '26',
      walletAddress: 'YhNu...7JmK',
      solBalance: 1678.9,
      pnl1d: 167.8,
      pnl7d: 334.5,
      pnl30d: 567.8,
      winRate7d: 72.1,
      transactions7d: 156,
      volume7d: 8.9,
      profit7d: 8.9,
      profit7dChart: [7.5, 8, 8.5, 9, 8.7, 9.1, 8.9],
      lastActivity: '8h ago',
      category: 'AI Agent Expert'
    },

    // Conservative Investor Wallets
    {
      id: '8',
      walletAddress: '5dDJ...XhUA',
      solBalance: 1170,
      pnl1d: 117.6,
      pnl7d: 104.6,
      pnl30d: 588.2,
      winRate7d: 57.7,
      transactions7d: 261,
      volume7d: 2.5,
      profit7d: 2.5,
      profit7dChart: [2.2, 2.3, 2.4, 2.6, 2.5, 2.6, 2.5],
      lastActivity: '9h ago',
      category: 'Conservative Investor'
    },
    {
      id: '27',
      walletAddress: 'UjIk...8OlP',
      solBalance: 4567.8,
      pnl1d: 91.3,
      pnl7d: 456.7,
      pnl30d: 1234.5,
      winRate7d: 89.2,
      transactions7d: 45,
      volume7d: 23.4,
      profit7d: 23.4,
      profit7dChart: [20, 21, 22, 23, 22.5, 23.8, 23.4],
      lastActivity: '11h ago',
      category: 'Conservative Investor'
    },
    {
      id: '28',
      walletAddress: 'PlOk...6MnB',
      solBalance: 3234.5,
      pnl1d: 64.6,
      pnl7d: 323.4,
      pnl30d: 967.8,
      winRate7d: 86.7,
      transactions7d: 38,
      volume7d: 16.7,
      profit7d: 16.7,
      profit7dChart: [14, 15, 16, 17, 16.2, 17.1, 16.7],
      lastActivity: '13h ago',
      category: 'Conservative Investor'
    },
    {
      id: '29',
      walletAddress: 'VcXz...9QwE',
      solBalance: 2789.3,
      pnl1d: 55.7,
      pnl7d: 278.9,
      pnl30d: 834.7,
      winRate7d: 83.4,
      transactions7d: 32,
      volume7d: 14.2,
      profit7d: 14.2,
      profit7dChart: [12, 13, 14, 15, 14.1, 14.8, 14.2],
      lastActivity: '15h ago',
      category: 'Conservative Investor'
    },
    {
      id: '30',
      walletAddress: 'BnMa...2SdF',
      solBalance: 1987.6,
      pnl1d: 39.7,
      pnl7d: 198.7,
      pnl30d: 596.2,
      winRate7d: 80.1,
      transactions7d: 27,
      volume7d: 9.8,
      profit7d: 9.8,
      profit7dChart: [8.5, 9, 9.5, 10, 9.7, 10.1, 9.8],
      lastActivity: '18h ago',
      category: 'Conservative Investor'
    }
  ];

  useEffect(() => {
    // Simulate API call
    const fetchSmartWalletData = async () => {
      setIsLoading(true);
      try {
        // Replace with actual API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setSmartWalletData(mockData);
      } catch (error) {
        console.error('Error fetching smart wallet data:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSmartWalletData();
  }, []);

  const filteredData = smartWalletData.filter(wallet => {
    if (activeFilter === 'All') return true;
    return wallet.category === activeFilter;
  });

  return (
    <div className="min-h-screen flex flex-col bg-[#141416]">
      <Navbar />

      <main className="flex-1 overflow-hidden">
        <div className="w-full px-4 sm:px-6 lg:px-8 h-full">
          <div className="py-6 h-full w-full">
            {/* Header */}
            <div className="mb-6">
              <h1 className="text-2xl font-bold text-white mb-2">Smart Wallet</h1>
              <p className="text-gray-400 text-sm">Track smart money movements and trading patterns</p>
            </div>

            {/* Filter Tabs */}
            <FilterTabs
              activeFilter={activeFilter}
              onFilterChange={setActiveFilter}
            />

            {/* Smart Wallet Table */}
            <div className="mt-6 w-full">
              <SmartWalletTable
                data={filteredData}
                isLoading={isLoading}
              />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SmartWallet;
