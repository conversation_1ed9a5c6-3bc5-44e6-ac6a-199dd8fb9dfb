import axios from 'axios';

// Get the backend API URL from environment variables with dynamic fallback
function getBackendApiUrl() {
  // Always use the proxy path for all environments
  return '/api';
}

const BACKEND_API_URL = getBackendApiUrl();

// TypeScript interfaces for API requests and responses
export interface ReferralCodeRequest {
  userId: string;
  referralCode: string;
}

export interface ReferralCodeResponse {
  success: boolean;
  message?: string;
  data?: {
    referralCode: string;
    referredBy?: string;
    appliedAt: string;
  };
  error?: string;
}

export interface UpdatePersonalReferralCodeRequest {
  userId: string;
  newReferralCode: string;
}

export interface UpdatePersonalReferralCodeResponse {
  success: boolean;
  message?: string;
  data?: {
    referralCode: string;
    updatedAt: string;
  };
  error?: string;
}

export interface RewardsDataRequest {
  userId: string;
}

export interface RewardsDataResponse {
  success: boolean;
  data?: {
    unclaimedRewards: number;
    totalEarned: number;
    totalReferrals: number;
    personalReferralCode: string;
    referralLevels: ReferralLevel[];
  };
  error?: string;
}

export interface ReferralLevel {
  level: number;
  commission: number;
  count: number;
  earnings: number;
  referrals: ReferralUser[];
}

export interface ReferralUser {
  id: string;
  username: string;
  joinDate: string;
  totalEarnings: number;
  isActive: boolean;
}

export interface ClaimRewardsRequest {
  userId: string;
  amount: number;
  walletAddress: string;
}

export interface ClaimRewardsResponse {
  success: boolean;
  data?: {
    transactionHash: string;
    amount: number;
    walletAddress: string;
    claimId: string;
  };
  error?: string;
}

export interface ClaimsHistoryRequest {
  userId: string;
  limit?: number;
  offset?: number;
  status?: 'completed' | 'pending' | 'failed';
  type?: 'referral' | 'bonus' | 'commission';
  startDate?: string;
  endDate?: string;
}

export interface ClaimRecord {
  id: string;
  date: string;
  amount: number;
  walletAddress: string;
  transactionHash: string;
  status: 'completed' | 'pending' | 'failed';
  type: 'referral' | 'bonus' | 'commission';
}

export interface ClaimsHistoryResponse {
  success: boolean;
  data?: {
    claims: ClaimRecord[];
    totalCount: number;
    totalClaimed: number;
  };
  error?: string;
}

/**
 * Save a referral code for a user
 */
export const saveReferralCode = async (
  userId: string,
  referralCode: string
): Promise<ReferralCodeResponse> => {
  try {
    console.log('Saving referral code via backend API:', {
      userId: userId.replace('did:privy:', ''),
      referralCode
    });

    const response = await axios.post<ReferralCodeResponse>(
      `${BACKEND_API_URL}/rewards/save-referral-code`,
      {
        userId: userId.replace('did:privy:', ''),
        referralCode: referralCode.toUpperCase()
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error saving referral code:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to save referral code'
    };
  }
};

/**
 * Get rewards data for a user
 */
export const getRewardsData = async (
  userId: string
): Promise<RewardsDataResponse> => {
  try {
    console.log('Fetching rewards data via backend API:', {
      userId: userId.replace('did:privy:', '')
    });

    const response = await axios.get<RewardsDataResponse>(
      `${BACKEND_API_URL}/rewards/data/${userId.replace('did:privy:', '')}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error fetching rewards data:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to fetch rewards data'
    };
  }
};

/**
 * Claim rewards for a user
 */
export const claimRewards = async (
  userId: string,
  amount: number,
  walletAddress: string
): Promise<ClaimRewardsResponse> => {
  try {
    console.log('Claiming rewards via backend API:', {
      userId: userId.replace('did:privy:', ''),
      amount,
      walletAddress
    });

    const response = await axios.post<ClaimRewardsResponse>(
      `${BACKEND_API_URL}/rewards/claim`,
      {
        userId: userId.replace('did:privy:', ''),
        amount,
        walletAddress
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 30000, // 30 second timeout for blockchain transactions
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error claiming rewards:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to claim rewards'
    };
  }
};

/**
 * Get claims history for a user
 */
export const getClaimsHistory = async (
  request: ClaimsHistoryRequest
): Promise<ClaimsHistoryResponse> => {
  try {
    console.log('Fetching claims history via backend API:', {
      userId: request.userId.replace('did:privy:', ''),
      ...request
    });

    const params = new URLSearchParams();
    if (request.limit) params.append('limit', request.limit.toString());
    if (request.offset) params.append('offset', request.offset.toString());
    if (request.status) params.append('status', request.status);
    if (request.type) params.append('type', request.type);
    if (request.startDate) params.append('startDate', request.startDate);
    if (request.endDate) params.append('endDate', request.endDate);

    const response = await axios.get<ClaimsHistoryResponse>(
      `${BACKEND_API_URL}/rewards/claims-history/${request.userId.replace('did:privy:', '')}?${params.toString()}`,
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error fetching claims history:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to fetch claims history'
    };
  }
};

/**
 * Generate referral link for a user
 */
export const generateReferralLink = (referralCode: string): string => {
  return `https://redfyn.crypfi.io/ref/${referralCode}`;
};

/**
 * Validate referral code format
 */
export const validateReferralCode = (code: string): boolean => {
  // Referral code should be 6-12 characters, alphanumeric
  const regex = /^[A-Z0-9]{6,12}$/;
  return regex.test(code.toUpperCase());
};

/**
 * Update user's personal referral code
 */
export const updatePersonalReferralCode = async (
  userId: string,
  newReferralCode: string
): Promise<UpdatePersonalReferralCodeResponse> => {
  try {
    console.log('Updating personal referral code via backend API:', {
      userId: userId.replace('did:privy:', ''),
      newReferralCode
    });

    const response = await axios.post<UpdatePersonalReferralCodeResponse>(
      `${BACKEND_API_URL}/rewards/update-personal-referral-code`,
      {
        userId: userId.replace('did:privy:', ''),
        newReferralCode: newReferralCode.toUpperCase()
      },
      {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error updating personal referral code:', error);
    return {
      success: false,
      error: error.response?.data?.error || error.message || 'Failed to update referral code'
    };
  }
};

/**
 * Get user's personal referral code from user ID
 */
export const generatePersonalReferralCode = (userId: string): string => {
  // Create a simple referral code from user ID (last 8 characters)
  const cleanUserId = userId.replace('did:privy:', '');
  return cleanUserId.slice(-8).toUpperCase();
};
