import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { FaExternalLinkAlt, FaFilter, FaSort, FaCalendarAlt, FaWallet, FaSpinner } from 'react-icons/fa';
import { getClaimsHistory, ClaimRecord } from '../api/rewards_api';

// ClaimRecord interface is now imported from rewards_api

const ClaimsHistory = () => {
  const { user } = usePrivy();
  const [claims, setClaims] = useState<ClaimRecord[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<ClaimRecord[]>([]);
  const [sortField, setSortField] = useState<keyof ClaimRecord>('date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [dateRange, setDateRange] = useState({ start: '', end: '' });
  const [isLoading, setIsLoading] = useState(false);

  // Load claims history
  useEffect(() => {
    if (user?.id) {
      loadClaimsHistory();
    }
  }, [user]);

  const loadClaimsHistory = async () => {
    if (!user?.id) return;

    setIsLoading(true);
    try {
      const response = await getClaimsHistory({
        userId: user.id,
        limit: 100,
        status: filterStatus !== 'all' ? filterStatus as any : undefined,
        type: filterType !== 'all' ? filterType as any : undefined,
        startDate: dateRange.start || undefined,
        endDate: dateRange.end || undefined,
      });

      if (response.success && response.data) {
        setClaims(response.data.claims);
      } else {
        setClaims([]);
      }
    } catch (error) {
      console.error('Error loading claims history:', error);
      setClaims([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Reload data when filters change
  useEffect(() => {
    if (user?.id) {
      loadClaimsHistory();
    }
  }, [filterStatus, filterType, dateRange]);

  // Apply sorting to filtered claims
  useEffect(() => {
    let filtered = [...claims];

    // Filtering is now done in the API call, so we just sort here

    // Sort
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      if (sortField === 'date') {
        aValue = new Date(aValue as string).getTime();
        bValue = new Date(bValue as string).getTime();
      } else if (sortField === 'amount') {
        aValue = Number(aValue);
        bValue = Number(bValue);
      }

      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredClaims(filtered);
  }, [claims, sortField, sortDirection]);

  const handleSort = (field: keyof ClaimRecord) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('desc');
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAddress = (address: string) => {
    if (address.length <= 12) return address;
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-[#7FFFD4]';
      case 'pending': return 'text-yellow-400';
      case 'failed': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'referral': return 'bg-blue-500/20 text-blue-400';
      case 'commission': return 'bg-green-500/20 text-green-400';
      case 'bonus': return 'bg-purple-500/20 text-purple-400';
      default: return 'bg-gray-500/20 text-gray-400';
    }
  };

  const getTotalClaimed = () => {
    return filteredClaims
      .filter(claim => claim.status === 'completed')
      .reduce((total, claim) => total + claim.amount, 0);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <FaSpinner className="animate-spin text-[#7FFFD4] text-3xl mr-4" />
        <span className="text-white text-lg">Loading claims history...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="text-2xl font-bold text-white mb-2">{filteredClaims.length}</div>
          <p className="text-gray-400 text-sm">Total Claims</p>
        </div>
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="text-2xl font-bold text-[#7FFFD4] mb-2">${getTotalClaimed().toFixed(2)}</div>
          <p className="text-gray-400 text-sm">Total Claimed</p>
        </div>
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="text-2xl font-bold text-green-400 mb-2">
            {filteredClaims.filter(c => c.status === 'completed').length}
          </div>
          <p className="text-gray-400 text-sm">Completed</p>
        </div>
        <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
          <div className="text-2xl font-bold text-yellow-400 mb-2">
            {filteredClaims.filter(c => c.status === 'pending').length}
          </div>
          <p className="text-gray-400 text-sm">Pending</p>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl p-6 border border-gray-800/50">
        <div className="flex items-center gap-4 mb-4">
          <FaFilter className="text-[#7FFFD4]" />
          <h3 className="text-lg font-semibold text-white">Filters</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm text-gray-400 mb-2">Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="w-full px-3 py-2 bg-[#0F1419] border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#7FFFD4]"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="pending">Pending</option>
              <option value="failed">Failed</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">Type</label>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="w-full px-3 py-2 bg-[#0F1419] border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#7FFFD4]"
            >
              <option value="all">All Types</option>
              <option value="referral">Referral</option>
              <option value="commission">Commission</option>
              <option value="bonus">Bonus</option>
            </select>
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">Start Date</label>
            <input
              type="date"
              value={dateRange.start}
              onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
              className="w-full px-3 py-2 bg-[#0F1419] border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#7FFFD4]"
            />
          </div>
          
          <div>
            <label className="block text-sm text-gray-400 mb-2">End Date</label>
            <input
              type="date"
              value={dateRange.end}
              onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
              className="w-full px-3 py-2 bg-[#0F1419] border border-gray-700 rounded-lg text-white focus:outline-none focus:border-[#7FFFD4]"
            />
          </div>
        </div>
      </div>

      {/* Claims Table */}
      <div className="bg-[#181C20]/80 backdrop-blur-sm rounded-xl border border-gray-800/50 overflow-hidden">
        <div className="p-6 border-b border-gray-700">
          <h3 className="text-lg font-semibold text-white">Claims History</h3>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-[#0F1419]">
              <tr>
                <th 
                  className="px-6 py-4 text-left text-sm font-medium text-gray-400 cursor-pointer hover:text-white"
                  onClick={() => handleSort('date')}
                >
                  <div className="flex items-center gap-2">
                    <FaCalendarAlt />
                    Date & Time
                    <FaSort className="text-xs" />
                  </div>
                </th>
                <th 
                  className="px-6 py-4 text-left text-sm font-medium text-gray-400 cursor-pointer hover:text-white"
                  onClick={() => handleSort('amount')}
                >
                  <div className="flex items-center gap-2">
                    Amount
                    <FaSort className="text-xs" />
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-400">Type</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-400">
                  <div className="flex items-center gap-2">
                    <FaWallet />
                    Wallet
                  </div>
                </th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-400">Transaction</th>
                <th className="px-6 py-4 text-left text-sm font-medium text-gray-400">Status</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {filteredClaims.map((claim) => (
                <tr key={claim.id} className="hover:bg-[#1A1F24] transition-colors">
                  <td className="px-6 py-4 text-sm text-white">
                    {formatDate(claim.date)}
                  </td>
                  <td className="px-6 py-4 text-sm font-semibold text-white">
                    ${claim.amount.toFixed(2)}
                  </td>
                  <td className="px-6 py-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(claim.type)}`}>
                      {claim.type}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-300 font-mono">
                    {formatAddress(claim.walletAddress)}
                  </td>
                  <td className="px-6 py-4">
                    <a
                      href={`https://solscan.io/tx/${claim.transactionHash}`}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-2 text-sm text-[#7FFFD4] hover:text-[#7FFFD4]/80 transition-colors"
                    >
                      {formatAddress(claim.transactionHash)}
                      <FaExternalLinkAlt className="text-xs" />
                    </a>
                  </td>
                  <td className="px-6 py-4">
                    <span className={`text-sm font-medium capitalize ${getStatusColor(claim.status)}`}>
                      {claim.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
          
          {filteredClaims.length === 0 && (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-2">No claims found</div>
              <div className="text-sm text-gray-500">Try adjusting your filters or make your first claim!</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClaimsHistory;
