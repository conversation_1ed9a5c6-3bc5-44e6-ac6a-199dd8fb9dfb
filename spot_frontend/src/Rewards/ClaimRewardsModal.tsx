import React, { useState, useEffect } from 'react';
import { usePrivy } from '@privy-io/react-auth';
import { useSolanaWallets } from '@privy-io/react-auth/solana';
import { FaTimes, FaWallet, Fa<PERSON><PERSON>ck, FaSpinner } from 'react-icons/fa';
import { toast } from 'react-toastify';
import { claimRewards } from '../api/rewards_api';
import { getTokenBalance, BalanceRequest } from '../api/solana_api';

interface ClaimRewardsModalProps {
  isOpen: boolean;
  onClose: () => void;
  unclaimedAmount: number;
  onClaimSuccess: (transactionHash: string, amount: number) => void;
}

interface WalletInfo {
  id: string;
  address: string;
  balance: number;
  isDefault: boolean;
  type: string;
}

const ClaimRewardsModal: React.FC<ClaimRewardsModalProps> = ({
  isOpen,
  onClose,
  unclaimedAmount,
  onClaimSuccess
}) => {
  const { user } = usePrivy();
  const { wallets: solanaWallets } = useSolanaWallets();
  const [selectedWallet, setSelectedWallet] = useState<string>('');
  const [walletInfos, setWalletInfos] = useState<WalletInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isClaiming, setIsClaiming] = useState(false);

  // Load wallet balances when modal opens
  useEffect(() => {
    if (isOpen && solanaWallets.length > 0) {
      loadWalletBalances();
    }
  }, [isOpen, solanaWallets]);

  const loadWalletBalances = async () => {
    setIsLoading(true);
    const walletInfosPromises = solanaWallets.map(async (wallet) => {
      try {
        const balanceRequest: BalanceRequest = {
          address: wallet.address,
          mint: 'So11111111111111111111111111111111111111112' // SOL mint address
        };
        
        const balanceResponse = await getTokenBalance(balanceRequest);
        const balance = balanceResponse.success ? balanceResponse.balance || 0 : 0;

        return {
          id: wallet.id,
          address: wallet.address,
          balance: balance,
          isDefault: wallet.id === solanaWallets[0]?.id, // First wallet as default
          type: wallet.walletClientType || 'embedded'
        };
      } catch (error) {
        console.error(`Error fetching balance for wallet ${wallet.address}:`, error);
        return {
          id: wallet.id,
          address: wallet.address,
          balance: 0,
          isDefault: wallet.id === solanaWallets[0]?.id,
          type: wallet.walletClientType || 'embedded'
        };
      }
    });

    const walletInfos = await Promise.all(walletInfosPromises);
    setWalletInfos(walletInfos);
    
    // Auto-select the first wallet
    if (walletInfos.length > 0) {
      setSelectedWallet(walletInfos[0].address);
    }
    
    setIsLoading(false);
  };

  const handleClaim = async () => {
    if (!selectedWallet) {
      toast.error('Please select a wallet to receive rewards');
      return;
    }

    if (!user?.id) {
      toast.error('User not authenticated');
      return;
    }

    if (unclaimedAmount <= 0) {
      toast.error('No rewards available to claim');
      return;
    }

    setIsClaiming(true);

    try {
      const response = await claimRewards(user.id, unclaimedAmount, selectedWallet);

      if (response.success && response.data) {
        toast.success(`Successfully claimed $${unclaimedAmount.toFixed(2)} rewards!`);
        onClaimSuccess(response.data.transactionHash, unclaimedAmount);
        onClose();
      } else {
        toast.error(response.error || 'Failed to claim rewards');
      }
    } catch (error) {
      console.error('Error claiming rewards:', error);
      toast.error('Failed to claim rewards. Please try again.');
    } finally {
      setIsClaiming(false);
    }
  };

  const formatAddress = (address: string) => {
    if (address.length <= 12) return address;
    return `${address.slice(0, 6)}...${address.slice(-6)}`;
  };

  const formatBalance = (balance: number) => {
    return balance.toFixed(4);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-[#181C20] rounded-xl border border-gray-800 max-w-md w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-700">
          <h2 className="text-xl font-semibold text-white">Claim Rewards</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white transition-colors"
          >
            <FaTimes size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {/* Claim Amount */}
          <div className="bg-[#0F1419] border border-gray-700 rounded-lg p-4">
            <div className="text-center">
              <div className="text-sm text-gray-400 mb-2">Amount to Claim</div>
              <div className="text-3xl font-bold text-[#7FFFD4]">
                ${unclaimedAmount.toFixed(2)}
              </div>
            </div>
          </div>

          {/* Wallet Selection */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">Select Destination Wallet</h3>
            
            {isLoading ? (
              <div className="flex items-center justify-center py-8">
                <FaSpinner className="animate-spin text-[#7FFFD4] text-2xl" />
                <span className="ml-3 text-gray-400">Loading wallet balances...</span>
              </div>
            ) : walletInfos.length === 0 ? (
              <div className="text-center py-8">
                <div className="text-gray-400 mb-2">No Solana wallets found</div>
                <div className="text-sm text-gray-500">Please connect a Solana wallet to claim rewards</div>
              </div>
            ) : (
              <div className="space-y-3">
                {walletInfos.map((wallet) => (
                  <div
                    key={wallet.id}
                    onClick={() => setSelectedWallet(wallet.address)}
                    className={`p-4 border rounded-lg cursor-pointer transition-all ${
                      selectedWallet === wallet.address
                        ? 'border-[#7FFFD4] bg-[#7FFFD4]/5'
                        : 'border-gray-700 hover:border-gray-600'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-[#7FFFD4]/10 rounded-full flex items-center justify-center">
                          <FaWallet className="text-[#7FFFD4]" />
                        </div>
                        <div>
                          <div className="text-white font-medium">
                            {formatAddress(wallet.address)}
                          </div>
                          <div className="text-sm text-gray-400">
                            {wallet.type === 'embedded' ? 'Embedded Wallet' : 'External Wallet'}
                            {wallet.isDefault && ' (Default)'}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-white font-semibold">
                          {formatBalance(wallet.balance)} SOL
                        </div>
                        {selectedWallet === wallet.address && (
                          <FaCheck className="text-[#7FFFD4] ml-auto mt-1" />
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Important Notice */}
          <div className="bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4">
            <div className="text-yellow-400 text-sm">
              <strong>Important:</strong> Rewards will be sent to the selected Solana wallet address. 
              Make sure you have access to this wallet before proceeding.
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex gap-3 p-6 border-t border-gray-700">
          <button
            onClick={onClose}
            className="flex-1 px-4 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={handleClaim}
            disabled={!selectedWallet || isClaiming || unclaimedAmount <= 0}
            className={`flex-1 px-4 py-3 rounded-lg font-semibold transition-colors flex items-center justify-center gap-2 ${
              selectedWallet && !isClaiming && unclaimedAmount > 0
                ? 'bg-[#7FFFD4] text-black hover:bg-[#7FFFD4]/90'
                : 'bg-gray-600 text-gray-400 cursor-not-allowed'
            }`}
          >
            {isClaiming ? (
              <>
                <FaSpinner className="animate-spin" />
                Claiming...
              </>
            ) : (
              'Claim Rewards'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ClaimRewardsModal;
