import React, { useState } from "react";
import Dashboard from "./Dashboard";
import ReferralTree from "./ReferralTree";
import ClaimsHistory from "./ClaimsHistory";

interface TabConfig {
  id: "Dashboard" | "ReferralTree" | "ClaimsHistory";
  label: string;
  icon: React.ReactNode;
}

const tabs: TabConfig[] = [
  {
    id: "Dashboard",
    label: "Dashboard",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    ),
  },
  {
    id: "ReferralTree",
    label: "Referral Tree",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" />
      </svg>
    ),
  },
  {
    id: "ClaimsHistory",
    label: "Claims History",
    icon: (
      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>
    ),
  },
];

const RewardsTabs = () => {
  const [activeTab, setActiveTab] = useState<"Dashboard" | "ReferralTree" | "ClaimsHistory">("Dashboard");

  const renderContent = () => {
    switch (activeTab) {
      case "Dashboard":
        return <Dashboard />;
      case "ReferralTree":
        return <ReferralTree />;
      case "ClaimsHistory":
        return <ClaimsHistory />;
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Modern pill-style tabs */}
      <div className="flex items-center mb-6 overflow-x-auto">
        <div className="relative bg-[#181C20] rounded-full p-1 flex min-w-max">
          {/* Animated background pill */}
          <div
            className="absolute top-1 bottom-1 bg-gradient-to-r from-[#7FFFD4]/20 to-[#7FFFD4]/10 rounded-full transition-all duration-300 ease-out"
            style={{
              left: activeTab === "Dashboard" ? "4px" : activeTab === "ReferralTree" ? "33.33%" : "66.66%",
              width: "calc(33.33% - 4px)",
            }}
          />

          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`relative z-10 flex items-center gap-2 px-4 sm:px-6 py-2 rounded-full text-xs sm:text-sm font-medium transition-all duration-300 whitespace-nowrap ${
                activeTab === tab.id
                  ? "text-white"
                  : "text-gray-400 hover:text-gray-200"
              }`}
            >
              <span className={`transition-all duration-300 ${
                activeTab === tab.id ? "text-[#7FFFD4]" : ""
              }`}>
                {tab.icon}
              </span>
              <span className="hidden sm:inline">{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      <div className="flex-1 h-full overflow-auto">{renderContent()}</div>
    </div>
  );
};

export default RewardsTabs;
