# RedFyn - Advanced Multi-Chain DEX with MEV Protection

A comprehensive cryptocurrency platform for traders and DeFi users featuring multi-chain support, advanced trading capabilities, MEV protection, and enterprise-grade wallet management.

![Trading Panel Screenshot](screenshot.png)

## 🚀 **Production Ready** | ✅ **Docker Containerized** | 🛡️ **MEV Protected** | 🔄 **Real-time Data**

## 🎯 Project Overview

RedFyn is an enterprise-grade, production-ready cryptocurrency platform featuring:

### 🏗️ **Microservices Architecture**
1. **🎨 Frontend Trading Panel** - Modern React-based UI with real-time updates and responsive design
2. **⚡ Spot Backend Service** - Core trading API with WebSocket support and Redis caching
3. **📊 Limit Orders Service** - Advanced order management with Supabase integration
4. **📈 Trading Panel Service** - Real-time data aggregation and analytics
5. **🔗 Solana Service** - Specialized blockchain integration with MEV protection and Jito bundles
6. **📚 Documentation Hub** - Comprehensive guides and implementation details

### 🌟 **Key Highlights**
- ✅ **Production Deployed**: Live at [redfyn.crypfi.io](https://redfyn.crypfi.io)
- 🐳 **Fully Containerized**: Docker images available on Docker Hub
- 🛡️ **MEV Protection**: Advanced Jito bundle integration for Solana trades
- 🔄 **Real-time Updates**: WebSocket-powered live data across all services
- 🌐 **Multi-Chain Support**: Ethereum, Solana, BSC, Polygon, and more
- 🚀 **High Performance**: Optimized caching, compression, and CDN delivery

## 🏗️ Architecture & Infrastructure

### 🌐 **Production Infrastructure**
```
Internet → Cloudflare CDN → Traefik (SSL/Routing) → Docker Services
                                    ↓
┌─────────────────┬─────────────────┬─────────────────┬─────────────────┐
│   Frontend      │  Spot Backend   │ Limit Orders    │ Trading Panel   │
│   (Nginx)       │   (Node.js)     │   (Node.js)     │   (Node.js)     │
│   Port: 80      │   Port: 5001    │   Port: 5002    │   Port: 5003    │
└─────────────────┴─────────────────┴─────────────────┴─────────────────┘
                              │
                    ┌─────────────────┐
                    │ Solana Service  │
                    │   (Node.js)     │
                    │   Port: 6001    │
                    └─────────────────┘
```

### 🐳 **Docker Containerization**
All services are containerized and available on Docker Hub:
- `prasanthats/redfyn:spot_frontend` - React frontend with optimized Nginx
- `prasanthats/redfyn:spot_backend` - Core backend API service
- `prasanthats/redfyn:solana` - Solana blockchain service with MEV protection
- `prasanthats/redfyn:trading_panel` - Real-time trading data service
- `prasanthats/redfyn:limit_orders` - Advanced order management service

### 📊 **Service Architecture**

#### 🎨 **Frontend Service** (`spot_frontend/`)
- **Port**: 4001 (Dev) / 80 (Prod)
- **Technology**: React + TypeScript + Vite + Privy + Socket.IO
- **Features**: Real-time UI, wallet integration, responsive design
- **Production**: Nginx with enhanced security headers and caching

#### ⚡ **Spot Backend Service** (`backend/spot_backend/`)
- **Port**: 5001
- **Technology**: Node.js + Express + TypeScript + Socket.IO + Redis
- **Features**: Core trading API, market data, WebSocket management
- **Integrations**: Mobula API, multi-chain balance fetching

#### 📊 **Limit Orders Service** (`backend/limit_orders/`)
- **Port**: 5002
- **Technology**: Node.js + Express + TypeScript + Supabase
- **Features**: Advanced order management, execution monitoring
- **Database**: Supabase for persistent order storage

#### 📈 **Trading Panel Service** (`backend/trading_panel/`)
- **Port**: 5003
- **Technology**: Node.js + Express + TypeScript + WebSocket
- **Features**: Real-time analytics, volume aggregation, trading metrics
- **Performance**: Redis caching for high-frequency data

#### 🔗 **Solana Service** (`backend/solana/`)
- **Port**: 6001
- **Technology**: Node.js + Express + TypeScript + Anchor + Raydium SDK
- **Features**: MEV protection, Jito bundles, Pump.fun integration
- **Security**: User-paid MEV protection, transaction optimization

## Project Structure

```
redfyn-spot/
├── spot_frontend/            # React frontend application (Port: 4001)
│   ├── src/
│   │   ├── components/       # Reusable UI components
│   │   │   ├── Trading/      # Trading panel components
│   │   │   ├── Wallet/       # Wallet connection components
│   │   │   └── UI/           # Generic UI elements
│   │   ├── hooks/            # Custom React hooks
│   │   ├── pages/            # Application pages/routes
│   │   ├── services/         # API services and external integrations
│   │   ├── store/            # State management (Redux/Context)
│   │   ├── utils/            # Helper functions and utilities
│   │   └── App.tsx           # Main application component
│   ├── public/               # Static assets
│   ├── .env                  # Environment variables
│   └── package.json          # Dependencies and scripts
├── backend/                  # Backend services directory
│   ├── spot_backend/         # Core trading backend (Port: 5001)
│   │   ├── src/
│   │   │   ├── controllers/  # API endpoint controllers
│   │   │   ├── routes/       # API route definitions (home, limit-orders, activity, trade-history)
│   │   │   ├── services/     # Business logic (pulse, websocket, mobula integration)
│   │   │   ├── utils/        # Helper functions and utilities
│   │   │   ├── middleware/   # Express middleware
│   │   │   ├── config/       # Configuration files
│   │   │   └── index.ts      # Application entry point with health checks
│   │   ├── dist/             # Compiled TypeScript
│   │   ├── docs/             # Implementation documentation
│   │   ├── scripts/          # Utility and test scripts
│   │   └── tsconfig.json     # TypeScript configuration
│   ├── limit_orders/         # Limit orders service (Port: 5002)
│   │   ├── src/
│   │   │   ├── controllers/  # Limit order controllers
│   │   │   ├── routes/       # API routes for limit orders
│   │   │   ├── services/     # Order monitoring and execution services
│   │   │   ├── middleware/   # Authentication and validation
│   │   │   ├── utils/        # Helper functions
│   │   │   └── index.ts      # Application entry point
│   │   ├── dist/             # Compiled TypeScript
│   │   └── logs/             # Service logs
│   ├── trading_panel/        # Trading panel service (Port: 5003)
│   │   ├── src/
│   │   │   ├── controllers/  # Trading panel controllers
│   │   │   ├── routes/       # API routes for trading data
│   │   │   ├── services/     # WebSocket, volume aggregation, Mobula integration
│   │   │   ├── types/        # TypeScript type definitions
│   │   │   └── index.ts      # Application entry point with WebSocket server
│   │   ├── dist/             # Compiled TypeScript
│   │   └── README.md         # Service-specific documentation
│   └── solana/               # Solana blockchain service (Port: 6001)
│       ├── src/
│       │   ├── controllers/  # Jupiter, Pump.fun, MEV, token controllers
│       │   ├── services/     # Anchor, Jito, transaction, analysis services
│       │   ├── routes/       # Solana-specific API routes
│       │   ├── types/        # Solana type definitions
│       │   ├── utils/        # Solana utilities
│       │   └── index.ts      # Application entry point
│       ├── idl/              # Anchor IDL files
│       ├── docs/             # MEV and Jito implementation guides
│       └── test/             # Solana-specific tests
├── scripts/                  # Utility scripts
│   ├── start-services.sh     # Service startup script
│   └── check-port.sh         # Port checking utility
├── memory-bank/              # Project documentation and context
├── nginx/                    # Nginx configuration for production
├── docker-compose.yml        # Development Docker setup
├── docker-compose.prod.yml   # Production Docker setup
├── package.json              # Root package.json with unified scripts
└── README.md                 # Project documentation
```

## 🌟 Core Features & Capabilities

### 🛡️ **MEV Protection & Advanced Trading**
- **Jito Bundle Integration**: User-paid MEV protection for Solana trades
- **Priority Fee Optimization**: Dynamic fee calculation based on network conditions
- **Sandwich Attack Prevention**: Advanced protection against front-running
- **Intelligent Fallback**: Graceful degradation when MEV protection unavailable
- **Transaction Optimization**: Bundle strategies for efficient execution

### 🎨 **Frontend Trading Experience**
- **Multi-Chain Support**: Trade across Ethereum, Solana, Polygon, Base, Arbitrum, Optimism
- **Privy Wallet Integration**: Seamless connection with embedded and external wallets
- **DEX Aggregation**: Best price discovery across multiple decentralized exchanges
- **Real-Time Quotes**: Sub-second price updates with 100ms debounce optimization
- **Advanced Order Types**: Market, Limit, Stop-Loss, Take-Profit orders
- **Responsive Design**: Mobile-first UI with intuitive trading controls
- **Live Data Streams**: WebSocket-powered real-time price and volume updates

#### Frontend Architecture
- **Component-Based Structure**: Modular components for maintainability and reusability
- **Responsive Layout**: Mobile-first design with Tailwind CSS
- **State Management**: Context API with reducers for global state
- **API Integration**: Custom hooks for data fetching with caching and invalidation
- **Websocket Integration**: Real-time price updates and order book data
- **Modular Routing**: Dynamic routing with code splitting for improved performance

#### Key UI Components
- **Trading Panel**: The core component for executing trades
  - Token selector with search and balance display
  - Order type selector (Market/Limit/Stop)
  - Price input with real-time validation
  - Slippage tolerance configuration
  - Transaction confirmation modal
- **Wallet Dashboard**: Interface for viewing and managing assets
  - Multi-chain balance overview
  - Token list with sorting and filtering
  - Transaction history with status indicators
- **Charts**: Interactive price charts with multiple timeframe options
  - TradingView integration
  - Custom indicators and drawing tools
  - Volume display and market depth visualization

### Backend (Wallet & Token Services)
- **Multi-Chain Wallet Balance API**: 
  - Fetch balances across ETH, BSC, Solana networks
  - Automatic token discovery with comprehensive metadata
  - Transaction history analysis for complete token detection
- **Token Metadata Service**: 
  - Dynamic token metadata retrieval from multiple sources
  - Token image, name and symbol fetching with smart caching
  - Support for newly created tokens via on-chain data
- **DEX Integration**:
  - Price and liquidity data from multiple DEXs
  - Trading pair information and token swap capabilities
  - Real-time quote aggregation

### Real-Time Data & WebSocket Services
- **Multi-Service WebSocket Architecture**:
  - Spot Backend: Market data aggregation and pulse data streaming
  - Trading Panel: Real-time trading metrics and volume analysis
  - Frontend: Live price updates and order book data
  - Mobula Integration: Real-time price feeds with fallback mechanisms

#### WebSocket Implementation
- **Frontend WebSocket Client**: 
  - Health check monitoring with automatic reconnection
  - Room-based subscriptions (pulse, trading, market data)
  - Connection status tracking and error handling
  - Callback system for different data types
- **Backend WebSocket Servers**: 
  - Socket.IO implementation with CORS configuration
  - Client registration and room management
  - Integration with external data sources (Mobula, DexScreener)
  - User activity tracking and session management
- **Data Flow Architecture**: 
  - Primary: WebSocket cache for real-time data
  - Fallback: REST API calls when WebSocket data is stale
  - Caching: Redis integration for performance optimization
  - Error Handling: Comprehensive fallback mechanisms

#### Advanced Trading Features
- **Limit Orders System**:
  - Advanced order creation and monitoring
  - Supabase integration for order persistence
  - Real-time order status updates
  - Execution tracking and notifications
- **MEV Protection (Solana)**:
  - Jito bundle integration for MEV protection
  - Priority fee optimization
  - Transaction confirmation improvements
  - User-paid Jito implementation
- **Multi-Chain Support**:
  - Ethereum, BSC, Solana network integration
  - Universal data aggregation across networks
  - Network-specific highlights and trending tokens
  - Cross-chain balance and token discovery

#### Technology Stack
- **WebSocket**: Socket.IO for real-time communication
- **Database**: Supabase for limit orders and user data
- **Caching**: Redis for performance optimization
- **Blockchain**: Ethers.js, Solana Web3.js, Anchor framework
- **External APIs**: Mobula, DexScreener, Jupiter, Pump.fun

## Technology Stack

### Frontend
- **React** - UI framework
- **TypeScript** - Type-safe code
- **Ethers.js** - Ethereum interaction
- **Privy** - Wallet connection
- **HeadlessUI** - UI components
- **Tailwind CSS** - Styling
- **React Query** - API data fetching and caching

### Backend
- **Node.js** - Runtime environment
- **Express** - Web framework
- **TypeScript** - Type-safe code
- **Ethers.js** - Blockchain interaction
- **Axios** - HTTP client
- **Solana Web3.js** - Solana blockchain integration
- **Socket.IO** - Real-time WebSocket communication
- **Supabase** - Database for limit orders and user data
- **Redis** - Caching and session management
- **Anchor** - Solana program framework
- **Raydium SDK** - Solana DEX integration

## 🔌 **API Endpoints**

### ⚡ **Spot Backend API** (Port: 5001)
#### **Market Data**
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>
- `GET /api/home/<USER>

#### **Trading & Orders**
- `GET /api/limit-orders/*` - Limit order management endpoints
- `GET /api/activity/*` - User activity tracking
- `GET /api/trade-history/*` - Trading history endpoints

#### **System**
- `GET /health` - Service health check
- `GET /websocket/health` - WebSocket service status
- `WebSocket /` - Real-time market data streams

### 📊 **Limit Orders API** (Port: 5002)
- `POST /api/limit-orders/create` - Create new limit orders
- `GET /api/limit-orders/user/:userId` - Get user's limit orders
- `PUT /api/limit-orders/:orderId` - Update limit order
- `DELETE /api/limit-orders/:orderId` - Cancel limit order
- `GET /api/limit-orders/status/:orderId` - Check order status
- `GET /health` - Service health check

### 📈 **Trading Panel API** (Port: 5003)
- `GET /api/trading-panel/volume` - Volume aggregation data
- `GET /api/trading-panel/metrics` - Trading metrics and analytics
- `GET /api/trading-panel/health` - Service health check
- `WebSocket /` - Real-time trading data streams

### 🔗 **Solana Service API** (Port: 6001)
#### **DEX Trading**
- `POST /api/jupiter/quote` - Jupiter DEX price quotes
- `POST /api/jupiter/swap` - Execute Jupiter swaps
- `POST /api/pumpfun/buy` - Pump.fun token purchases
- `POST /api/pumpfun/sell` - Pump.fun token sales

#### **MEV Protection** 🛡️
- `POST /api/mev/quote` - MEV-protected price quotes
- `POST /api/mev/swap` - MEV-protected swap execution
- `POST /api/mev/bundle` - Create Jito bundles for MEV protection

#### **Token & Metadata**
- `POST /api/token/metadata` - Solana token metadata
- `GET /api/token/balance/:address` - Token balance queries
- `GET /api/test/connection` - Test Solana RPC connection

#### **System**
- `GET /health` - Service health check

## 🚀 Getting Started

### 🐳 **Docker Deployment (Recommended)**

#### **Production Deployment**
```bash
# Clone the repository
git clone https://github.com/prasanthlrb/RedFyn_spot.git
cd RedFyn_spot

# Build and push Docker images
sudo ./scripts/build-push.sh

# Deploy to production
sudo ./scripts/deploy.sh
```

#### **Local Docker Development**
```bash
# Start all services with Docker Compose
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### 💻 **Local Development (All Services)**

Install dependencies and start all services with a single command:

```bash
# Install dependencies for all services
npm install

# Start all services concurrently
npm run dev
```

**Services will be available at:**
- 🎨 Frontend: http://localhost:4001
- ⚡ Spot Backend: http://localhost:5001
- 📊 Limit Orders: http://localhost:5002
- 📈 Trading Panel: http://localhost:5003
- 🔗 Solana Service: http://localhost:6001

### Individual Service Development

#### Frontend Development
```bash
cd spot_frontend
npm install
npm run dev
# Runs on http://localhost:4001
```

#### Spot Backend Development
```bash
cd backend/spot_backend
npm install
npm run dev
# Runs on http://localhost:5001
```

#### Limit Orders Service Development
```bash
cd backend/limit_orders
npm install
npm run dev
# Runs on http://localhost:5002
```

#### Trading Panel Service Development
```bash
cd backend/trading_panel
npm install
npm run dev
# Runs on http://localhost:5003
```

#### Solana Service Development
```bash
cd backend/solana
npm install
npm run dev
# Runs on http://localhost:6001
```

### 📋 **Available Scripts**

#### **🐳 Docker Commands**
```bash
# Build and push all Docker images
sudo ./scripts/build-push.sh

# Deploy to production with Traefik
sudo ./scripts/deploy.sh

# Local Docker development
docker-compose up -d              # Start all services
docker-compose logs -f            # View logs
docker-compose down               # Stop services
```

#### **💻 Development Commands**
```bash
# Development
npm run dev                    # Start all services concurrently
npm run dev:frontend          # Start only frontend
npm run dev:spot-backend      # Start only spot backend
npm run dev:limit-orders      # Start only limit orders service
npm run dev:trading-panel     # Start only trading panel service
npm run dev:solana            # Start only solana service

# Installation
npm run install:all           # Install dependencies for all services
npm run install:frontend      # Install frontend dependencies
npm run install:backends      # Install all backend dependencies

# Building
npm run build:all             # Build all services
npm run build:frontend        # Build frontend
npm run build:backends        # Build all backend services

# Testing
npm run test:all              # Run tests for all services
npm run test:frontend         # Run frontend tests
npm run test:backends         # Run backend tests

# Utilities
npm run clean                 # Clean all node_modules and dist folders
npm run ports                 # Check which ports are in use
```

### Troubleshooting

#### Common Issues

1. **Permission Denied Errors**
   ```bash
   # Fix node_modules binary permissions
   chmod -R +x */node_modules/.bin/*
   ```

2. **Port Already in Use**
   ```bash
   # Check which services are running
   npm run ports

   # Kill processes on specific ports
   sudo lsof -ti:4001 | xargs kill -9  # Frontend
   sudo lsof -ti:5001 | xargs kill -9  # Spot Backend
   sudo lsof -ti:5002 | xargs kill -9  # Limit Orders
   sudo lsof -ti:5003 | xargs kill -9  # Trading Panel
   sudo lsof -ti:6001 | xargs kill -9  # Solana Service
   ```

3. **Redis Connection Errors**
   ```bash
   # Install and start Redis (optional)
   sudo apt-get install redis-server
   sudo systemctl start redis-server
   ```

4. **Missing Dependencies**
   ```bash
   # Reinstall all dependencies
   npm run clean
   npm run install:all
   ```

#### Service Health Check

Use the built-in port checker to verify all services are running:
```bash
npm run ports
```

Expected output when all services are running:
```
✓ Frontend (Port 4001): RUNNING
✓ Spot Backend (Port 5001): RUNNING
✓ Limit Orders (Port 5002): RUNNING
✓ Trading Panel (Port 5003): RUNNING
✓ Solana Service (Port 6001): RUNNING
```

## ⚙️ **Environment Configuration**

### 🔧 **Production Environment Variables**

#### **Frontend (.env in spot_frontend/)**
```bash
# Production URLs
REACT_APP_API_URL=https://redfyn.crypfi.io/api
REACT_APP_SOLANA_API_URL=https://redfyn.crypfi.io/solana-api
REACT_APP_TRADING_API_URL=https://redfyn.crypfi.io/api/trading-panel-api
REACT_APP_LIMIT_ORDERS_API_URL=https://redfyn.crypfi.io/api/limit-orders-api

# Wallet Integration
VITE_PRIVY_APP_ID=your_privy_app_id
NODE_ENV=production
```

#### **Spot Backend (.env in backend/spot_backend/)**
```bash
PORT=5001
NODE_ENV=production
ALCHEMY_API_KEY_ETH=your_alchemy_key
BSCSCAN_API_KEY=your_bscscan_key
ETHERSCAN_API_KEY=your_etherscan_key
SOLANA_RPC_URL=your_solana_rpc
REDIS_URL=redis://localhost:6379
MOBULA_API_KEY=your_mobula_api_key
```

#### **Solana Service (.env in backend/solana/)**
```bash
PORT=6001
NODE_ENV=production
SOLANA_RPC_URL=https://api.mainnet-beta.solana.com
SOLANA_DEVNET_RPC_URL=https://api.devnet.solana.com

# Jito Configuration for MEV Protection
JITO_RPC_URL_1=https://mainnet.block-engine.jito.wtf/api/v1
JITO_RPC_URL_2=https://amsterdam.mainnet.block-engine.jito.wtf/api/v1
JITO_RPC_URL_3=https://frankfurt.mainnet.block-engine.jito.wtf/api/v1
JITO_RPC_URL_4=https://ny.mainnet.block-engine.jito.wtf/api/v1
JITO_RPC_URL_5=https://tokyo.mainnet.block-engine.jito.wtf/api/v1

# MEV Protection Settings
MEV_TIP_ACCOUNT=96gYZGLnJYVFmbjzopPSU6QiEV5fGqZNyN9nmNhvrZU5
MEV_TIP_ACCOUNT_2=HFqU5x63VTqvQss8hp11i4wVV8bD44PvwucfZ2bU7gRe
MEV_TIP_ACCOUNT_3=Cw8CFyM9FkoMi7K7Crf6HNQqf4uEMzpKw6QNghXLvLkY
MEV_TIP_ACCOUNT_4=ADaUMid9yfUytqMBgopwjb2DTLSokTSzL1zt6iGPaS49
MEV_TIP_ACCOUNT_5=DfXygSm4jCyNCybVYYK6DwvWqjKee8pbDmJGcLWNDXjh
MEV_TIP_ACCOUNT_6=ADuUkR4vqLUMWXxW9gh6D6L8pMSawimctcNZ5pGwDcEt
MEV_TIP_ACCOUNT_7=DttWaMuVvTiduZRnguLF7jNxTgiMBZ1hyAumKUiL2KRL
MEV_TIP_ACCOUNT_8=3AVi9Tg9Uo68tJfuvoKvqKNWKkC5wPdSSdeBnizKZ6jT
```

#### **Limit Orders Service (.env in backend/limit_orders/)**
```bash
PORT=5002
NODE_ENV=production
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key
```

#### **Trading Panel Service (.env in backend/trading_panel/)**
```bash
PORT=5003
NODE_ENV=production
MOBULA_API_KEY=your_mobula_api_key
REDIS_URL=redis://localhost:6379
```

### 🔧 **Development Environment**

For local development, use localhost URLs:
```bash
# Frontend Development URLs
VITE_API_URL=http://localhost:5001/api
VITE_TRADING_PANEL_API_URL=http://localhost:5003/api
VITE_SOLANA_API_URL=http://localhost:6001/api
VITE_LIMIT_ORDERS_API_URL=http://localhost:5002/api
```

## 🛡️ **MEV Protection & Security**

### **Advanced MEV Protection**
- **Jito Bundle Integration**: User-paid tips to Jito validators for MEV protection
- **Dynamic Tip Calculation**: Smart tip amounts based on trade size and priority level
- **Multiple Jito Endpoints**: Failover across 5+ Jito block engines for reliability
- **Intelligent Fallback**: Graceful degradation to regular execution when Jito unavailable
- **Risk Assessment**: Automatic MEV risk scoring based on trade characteristics
- **Priority Fee Optimization**: Dynamic fee calculation for optimal execution

### **Security Features**
- ✅ **Real-time Balance Validation**: Continuous balance checking and insufficient funds detection
- ✅ **Price Impact Warnings**: High slippage and price impact alerts
- ✅ **Transaction Transparency**: Complete fee breakdown and transaction preview
- ✅ **Input Validation**: Comprehensive validation and error handling
- ✅ **Fallback Mechanisms**: Multiple API fallbacks for high availability
- ✅ **Secure Headers**: Enhanced security headers in production nginx configuration
- ✅ **CORS Protection**: Proper cross-origin resource sharing configuration

## 🐳 **Docker & Production Deployment**

### **Docker Images**
All services are available as production-ready Docker images:
```bash
# Pull latest images
docker pull prasanthats/redfyn:spot_frontend
docker pull prasanthats/redfyn:spot_backend
docker pull prasanthats/redfyn:solana
docker pull prasanthats/redfyn:trading_panel
docker pull prasanthats/redfyn:limit_orders
```

### **Production Infrastructure**
- **🌐 Domain**: [redfyn.crypfi.io](https://redfyn.crypfi.io)
- **🔒 SSL**: Automatic HTTPS with Let's Encrypt via Traefik
- **⚡ CDN**: Cloudflare for global content delivery
- **🐳 Containers**: Docker Swarm for orchestration
- **📊 Monitoring**: Health checks and service monitoring
- **🔄 Load Balancing**: Traefik reverse proxy with automatic service discovery

### **Performance Optimizations**
- **Frontend**: Nginx with gzip compression, static asset caching, and security headers
- **Backend**: Redis caching for high-frequency data and API responses
- **Database**: Supabase with connection pooling and query optimization
- **WebSocket**: Optimized real-time connections with automatic reconnection
- **Bundle Size**: Code splitting and lazy loading for optimal loading times

## 📊 **Monitoring & Analytics**

### **Health Checks**
- Service-level health endpoints for all microservices
- Docker container health monitoring
- Database connection status monitoring
- External API dependency health checks

### **Performance Metrics**
- Real-time transaction success rates
- MEV protection effectiveness tracking
- API response time monitoring
- WebSocket connection stability metrics

## 🤝 **Contributing**

### **Development Workflow**
1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Make your changes and test thoroughly
4. Commit your changes: `git commit -m 'Add amazing feature'`
5. Push to the branch: `git push origin feature/amazing-feature`
6. Open a Pull Request

### **Code Standards**
- TypeScript for type safety
- ESLint and Prettier for code formatting
- Comprehensive error handling
- Unit tests for critical functionality
- Documentation for new features

## 📚 **Documentation**

- **📖 API Documentation**: Comprehensive API endpoint documentation
- **🛡️ MEV Implementation**: Detailed MEV protection implementation guide
- **🐳 Docker Guide**: Complete containerization and deployment guide
- **⚙️ Configuration**: Environment setup and configuration guide
- **🔧 Troubleshooting**: Common issues and solutions

## 📄 **License**

MIT License - see the [LICENSE](LICENSE) file for details.

---

**🚀 Built with ❤️ for the DeFi community | Production Ready | Enterprise Grade**